const { Connection, PublicKey, Keypair, Transaction, SystemProgram, ComputeBudgetProgram } = require('@solana/web3.js');
const { BN } = require('@coral-xyz/anchor');
const fs = require('fs');

// Исследуем что экспортирует SDK
console.log('🔍 ИССЛЕДУЕМ METEORA SDK...');
const dlmmModule = require('@meteora-ag/dlmm');
console.log('🔍 SDK MODULE:', Object.keys(dlmmModule));

// Пробуем разные варианты импорта SDK
let DLMM;
if (dlmmModule.default) {
    DLMM = dlmmModule.default;
    console.log('✅ DLMM найден как .default');
} else if (dlmmModule.DLMM) {
    DLMM = dlmmModule.DLMM;
    console.log('✅ DLMM найден как .DLMM');
} else {
    console.log('❌ DLMM НЕ НАЙДЕН, пробуем другие варианты...');
    // Ищем функции с create методом
    for (const [key, value] of Object.entries(dlmmModule)) {
        if (value && typeof value === 'object' && value.create) {
            DLMM = value;
            console.log(`✅ Найден ${key} с методом create`);
            break;
        }
    }
}

if (!DLMM) {
    console.error('❌ DLMM не найден!');
    process.exit(1);
}

// Конфигурация
const RPC_ENDPOINT = 'https://api.mainnet-beta.solana.com';
const connection = new Connection(RPC_ENDPOINT, 'confirmed');

// Загружаем кошелек
const walletData = JSON.parse(fs.readFileSync('./wallet.json', 'utf8'));
const wallet = Keypair.fromSecretKey(new Uint8Array(walletData));

console.log('🔍 SDK Interceptor для пустых бинов запущен');
console.log('📍 Кошелек:', wallet.publicKey.toString());

// Перехватываем все методы DLMM
const originalDLMM = DLMM.prototype;

// Список методов для перехвата
const methodsToIntercept = [
    'initializePositionAndAddLiquidityByStrategy',
    'addLiquidityByStrategy', 
    'initializeBinArrays',
    'createBinArraysIfNeeded',
    'seedLiquidity',
    'seedLiquiditySingleBin'
];

// Создаем перехватчики
methodsToIntercept.forEach(methodName => {
    if (originalDLMM[methodName]) {
        const originalMethod = originalDLMM[methodName];
        
        originalDLMM[methodName] = async function(...args) {
            console.log(`\n🎯 ПЕРЕХВАЧЕН ВЫЗОВ: ${methodName}`);
            console.log('📥 Аргументы:', JSON.stringify(args, (key, value) => {
                if (value && typeof value === 'object' && value.constructor && value.constructor.name === 'PublicKey') {
                    return value.toString();
                }
                if (value && typeof value === 'object' && value.constructor && value.constructor.name === 'BN') {
                    return value.toString();
                }
                return value;
            }, 2));
            
            try {
                const result = await originalMethod.apply(this, args);
                console.log(`✅ РЕЗУЛЬТАТ ${methodName}:`);
                
                // Анализируем результат
                if (result && result.instructions) {
                    console.log('📋 Транзакция с', result.instructions.length, 'инструкциями');
                    
                    result.instructions.forEach((ix, index) => {
                        console.log(`\n📝 Инструкция ${index + 1}:`);
                        console.log('  Программа:', ix.programId.toString());
                        console.log('  Аккаунты:', ix.keys.length);
                        
                        // Анализируем аккаунты
                        ix.keys.forEach((key, keyIndex) => {
                            console.log(`    ${keyIndex + 1}. ${key.pubkey.toString()} (${key.isSigner ? 'signer' : 'nosign'}, ${key.isWritable ? 'write' : 'read'})`);
                        });
                        
                        // Проверяем дискриминатор
                        if (ix.data.length >= 8) {
                            const discriminator = Array.from(ix.data.slice(0, 8));
                            console.log('  Дискриминатор:', discriminator.join(','));
                            
                            // Известные дискриминаторы
                            const knownDiscriminators = {
                                '35,86,19,185,78,212,75,211': 'initializeBinArray',
                                '47,157,226,180,12,240,33,71': 'initializeBinArrayBitmapExtension',
                                '181,157,89,67,143,182,52,72': 'addLiquidity',
                                '228,162,78,28,70,219,116,115': 'addLiquidity2',
                                '7,3,150,127,148,40,61,200': 'addLiquidityByStrategy',
                                '3,221,149,218,111,141,118,213': 'addLiquidityByStrategy2'
                            };
                            
                            const discriminatorStr = discriminator.join(',');
                            if (knownDiscriminators[discriminatorStr]) {
                                console.log('  ✅ Распознано:', knownDiscriminators[discriminatorStr]);
                            }
                        }
                    });
                } else if (Array.isArray(result)) {
                    console.log('📋 Массив инструкций:', result.length);
                    result.forEach((ix, index) => {
                        if (ix.programId) {
                            console.log(`  ${index + 1}. ${ix.programId.toString()}`);
                            
                            // Проверяем дискриминатор для каждой инструкции
                            if (ix.data && ix.data.length >= 8) {
                                const discriminator = Array.from(ix.data.slice(0, 8));
                                const discriminatorStr = discriminator.join(',');
                                
                                const knownDiscriminators = {
                                    '35,86,19,185,78,212,75,211': 'initializeBinArray',
                                    '47,157,226,180,12,240,33,71': 'initializeBinArrayBitmapExtension'
                                };
                                
                                if (knownDiscriminators[discriminatorStr]) {
                                    console.log(`    ✅ ${knownDiscriminators[discriminatorStr]}`);
                                }
                            }
                        }
                    });
                } else {
                    console.log('📋 Результат:', typeof result);
                }
                
                // Сохраняем детальную информацию
                const interceptData = {
                    timestamp: new Date().toISOString(),
                    method: methodName,
                    arguments: args,
                    poolAddress: this.pubkey?.toString(),
                    activeId: this.lbPair?.activeId,
                    resultType: typeof result,
                    instructionsCount: result?.instructions?.length || (Array.isArray(result) ? result.length : 0)
                };
                
                fs.appendFileSync('./empty-bin-intercept-log.json', JSON.stringify(interceptData, null, 2) + '\n');
                
                return result;
            } catch (error) {
                console.error(`❌ ОШИБКА в ${methodName}:`, error);
                throw error;
            }
        };
    }
});

async function testEmptyBinLiquidity() {
    try {
        console.log('\n🚀 Тестируем добавление ликвидности в пустой бин...');
        
        // WSOL-USDC пул из нашего рабочего кода
        const poolAddress = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');
        
        console.log('📊 Загружаем пул:', poolAddress.toString());
        const dlmm = await DLMM.create(connection, poolAddress);
        
        console.log('🎯 Активный бин ID:', dlmm.lbPair.activeId);
        console.log('💰 Резервы X:', dlmm.lbPair.reserveX.toString());
        console.log('💰 Резервы Y:', dlmm.lbPair.reserveY.toString());
        
        // Находим пустой бин рядом с активным
        const activeBinId = dlmm.lbPair.activeId;

        // Попробуем найти bin array, который еще не инициализирован
        // Активный бин: -4250, попробуем бин намного дальше
        const targetBinId = activeBinId + 1000; // Бин намного выше активного

        console.log('🎯 Целевой бин ID:', targetBinId);

        // Проверяем, что бин пустой
        const binArrayIndex = Math.floor(targetBinId / 70);
        console.log('📦 Индекс bin array:', binArrayIndex);

        // Проверяем существование bin array
        const { deriveBinArray } = require('@meteora-ag/dlmm');
        const [binArrayPubkey] = deriveBinArray(
            dlmm.pubkey,
            new BN(binArrayIndex),
            dlmm.program.programId
        );

        console.log('📦 Bin Array PDA:', binArrayPubkey.toString());

        const binArrayAccount = await connection.getAccountInfo(binArrayPubkey);
        console.log('📦 Bin Array существует:', binArrayAccount !== null);
        
        // Создаем стратегию для одного бина
        const strategy = {
            strategyType: 'SpotOneSide',
            minBinId: targetBinId,
            maxBinId: targetBinId,
            singleSidedX: true // Добавляем только токен X
        };
        
        // Небольшая сумма для теста
        const totalXAmount = new BN(1000000); // 0.001 SOL
        const totalYAmount = new BN(0);
        
        console.log('💡 Стратегия:', strategy);
        console.log('💰 Сумма X:', totalXAmount.toString());
        console.log('💰 Сумма Y:', totalYAmount.toString());
        
        // Создаем новую позицию
        const positionKeypair = Keypair.generate();
        console.log('🔑 Новая позиция:', positionKeypair.publicKey.toString());
        
        // Попробуем напрямую вызвать initializeBinArrays для нового bin array
        console.log('\n🎯 ТЕСТИРУЕМ ПРЯМОЙ ВЫЗОВ initializeBinArrays...');

        // Найдем bin array, который точно не существует
        let testBinArrayIndex = 1000; // Очень далекий индекс
        let foundUninitialized = false;

        for (let i = 1000; i < 1010; i++) {
            const [testBinArrayPubkey] = deriveBinArray(
                dlmm.pubkey,
                new BN(i),
                dlmm.program.programId
            );

            const testBinArrayAccount = await connection.getAccountInfo(testBinArrayPubkey);
            if (testBinArrayAccount === null) {
                testBinArrayIndex = i;
                foundUninitialized = true;
                console.log(`✅ Найден неинициализированный bin array с индексом ${i}: ${testBinArrayPubkey.toString()}`);
                break;
            }
        }

        if (!foundUninitialized) {
            console.log('❌ Не удалось найти неинициализированный bin array');
            return;
        }

        // Вызываем initializeBinArrays напрямую
        console.log('\n🎯 ВЫЗЫВАЕМ SDK метод initializeBinArrays...');

        const initBinArrayInstructions = await dlmm.initializeBinArrays(
            [new BN(testBinArrayIndex)],
            wallet.publicKey
        );

        if (initBinArrayInstructions && initBinArrayInstructions.instructions) {
            console.log('📋 Инструкции initializeBinArrays:', initBinArrayInstructions.instructions.length);
        } else {
            console.log('📋 Результат initializeBinArrays:', typeof initBinArrayInstructions);
        }

        // Также попробуем создать позицию в этом bin array
        console.log('\n🎯 ВЫЗЫВАЕМ SDK метод initializePositionAndAddLiquidityByStrategy...');

        const transaction = await dlmm.initializePositionAndAddLiquidityByStrategy({
            positionPubKey: positionKeypair.publicKey,
            totalXAmount,
            totalYAmount,
            strategy,
            user: wallet.publicKey,
            slippage: 1 // 1%
        });

        console.log('\n✅ Тест завершен успешно!');
        
    } catch (error) {
        console.error('❌ Ошибка в тесте:', error);
    }
}

// Запускаем тест
testEmptyBinLiquidity();
