/**
 * 🔥 ОТДЕЛЬНАЯ ТРАНЗАКЦИЯ ДЛЯ INITIALIZE_POSITION
 * ТОЛЬКО СОЗДАНИЕ ПОЗИЦИЙ БЕЗ FLASH LOAN
 * РЕШАЕТ КОНФЛИКТ С ЗАЙМОМ
 */

const { Connection, Keypair, PublicKey, TransactionInstruction, ComputeBudgetProgram, TransactionMessage, VersionedTransaction, Transaction, SystemProgram } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;
const { AnchorProvider, Wallet } = require('@coral-xyz/anchor');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.solana' });

class InitializePositionsOnly {
    constructor() {
        // 🔥 RPC ПРОВАЙДЕРЫ ПРЯМО В СКРИПТЕ
        this.rpcProviders = [
            'https://api.mainnet-beta.solana.com',
            process.env.HELIUS_RPC_URL,
            process.env.HELIUS_RPC_URL_2,
            process.env.HELIUS_RPC_URL_3,
            'https://bold-omniscient-arm.solana-mainnet.quiknode.pro/b11b7e86a3929c21297a0e50515cca99b1fd218f/'
        ].filter(Boolean);

        this.currentRpcIndex = 0;
        const rpcUrl = this.rpcProviders[this.currentRpcIndex];
        this.connection = new Connection(rpcUrl, {
            commitment: 'confirmed',
            confirmTransactionInitialTimeout: 60000,
            disableRetryOnRateLimit: false
        });
        
        // Wallet из wallet.json
        const secretKey = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
        this.wallet = Keypair.fromSecretKey(new Uint8Array(secretKey));
        
        console.log(`🌐 RPC: ${rpcUrl.substring(0, 50)}... (${this.rpcProviders.length} провайдеров доступно)`);
        console.log(`💰 Wallet: ${this.wallet.publicKey.toString()}`);
        
        // Meteora DLMM Program
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        
        // Пулы для создания позиций (АКТИВНЫЕ BINS БУДУТ ПОЛУЧЕНЫ ДИНАМИЧЕСКИ!)
        this.POOLS = {
            POOL_1: {
                address: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
                activeBinId: null, // 🔥 БУДЕТ ПОЛУЧЕН ДИНАМИЧЕСКИ!
                binStep: 4 // 4 basis points
            },
            POOL_2: {
                address: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
                activeBinId: null, // 🔥 БУДЕТ ПОЛУЧЕН ДИНАМИЧЕСКИ!
                binStep: 10 // 10 basis points
            }
        };
        
        console.log('🔥 Initialize Positions Only - инициализирован');
    }

    // 🔄 ПЕРЕКЛЮЧЕНИЕ RPC ПРИ ОШИБКАХ
    switchToNextRPC() {
        this.currentRpcIndex = (this.currentRpcIndex + 1) % this.rpcProviders.length;
        const newRpcUrl = this.rpcProviders[this.currentRpcIndex];
        this.connection = new Connection(newRpcUrl, {
            commitment: 'confirmed',
            confirmTransactionInitialTimeout: 60000,
            disableRetryOnRateLimit: false
        });
        console.log(`🔄 ПЕРЕКЛЮЧЕНИЕ RPC: ${newRpcUrl.substring(0, 50)}... (${this.currentRpcIndex + 1}/${this.rpcProviders.length})`);
        return newRpcUrl;
    }

    // 🌐 ВЫПОЛНЕНИЕ RPC ОПЕРАЦИИ С RETRY
    async executeWithRetry(operation, maxRetries = 5) {
        let lastError;

        for (let attempt = 0; attempt < maxRetries; attempt++) {
            try {
                console.log(`   🔄 Попытка ${attempt + 1}/${maxRetries} через RPC: ${this.rpcProviders[this.currentRpcIndex].substring(0, 30)}...`);
                return await operation(this.connection);
            } catch (error) {
                lastError = error;
                console.log(`   ❌ Попытка ${attempt + 1}/${maxRetries} провалилась: ${error.message}`);

                if (attempt < maxRetries - 1) {
                    this.switchToNextRPC();
                    const delay = Math.min(2000 * (attempt + 1), 10000); // Увеличивающаяся задержка
                    console.log(`   ⏳ Ждем ${delay}ms перед следующей попыткой...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        console.log(`   💥 ВСЕ ${maxRetries} ПОПЫТОК ПРОВАЛИЛИСЬ!`);
        throw lastError;
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ ТЕКУЩИХ АКТИВНЫХ BIN ID ИЗ ПУЛОВ
     */
    async getCurrentActiveBinIds() {
        console.log('🔥 ПОЛУЧАЕМ ТЕКУЩИЕ АКТИВНЫЕ BIN ID ИЗ ПУЛОВ...');

        for (const [poolName, poolData] of Object.entries(this.POOLS)) {
            try {
                console.log(`📊 Получаем активный bin для ${poolName} (${poolData.address.slice(0,8)}...)...`);

                // Создаем DLMM инстанс с retry логикой
                const dlmm = await this.executeWithRetry(async (connection) => {
                    return await DLMM.create(connection, new PublicKey(poolData.address));
                });

                // Обновляем состояние пула с retry логикой
                await this.executeWithRetry(async () => {
                    return await dlmm.refetchStates();
                });

                // Получаем текущий активный bin ID
                const currentActiveBinId = dlmm.lbPair.activeId;

                // Сохраняем в конфигурацию
                poolData.activeBinId = currentActiveBinId;

                console.log(`✅ ${poolName}: Активный bin ID = ${currentActiveBinId}`);

                // Получаем цену для информации
                try {
                    const activeBin = await dlmm.getActiveBin();
                    const price = dlmm.fromPricePerLamport(Number(activeBin.price));
                    console.log(`   💰 Цена: $${Number(price).toFixed(4)}`);
                } catch (priceError) {
                    console.log(`   💰 Цена: не удалось получить (${priceError.message})`);
                }

            } catch (error) {
                console.log(`❌ Ошибка получения активного bin для ${poolName}: ${error.message}`);
                throw error;
            }
        }

        console.log('✅ ВСЕ АКТИВНЫЕ BIN ID ПОЛУЧЕНЫ!');
        console.log(`   Pool 1: ${this.POOLS.POOL_1.activeBinId}`);
        console.log(`   Pool 2: ${this.POOLS.POOL_2.activeBinId}`);
    }

    /**
     * 🔥 ОБНОВЛЕНИЕ TRADING-CONFIG.JS С НОВЫМИ ПОЗИЦИЯМИ
     */
    async updateTradingConfig(newPositions) {
        console.log('\n🔥 ОБНОВЛЯЕМ TRADING-CONFIG.JS С НОВЫМИ ПОЗИЦИЯМИ...');

        const configPath = path.join(__dirname, 'trading-config.js');

        try {
            // Читаем текущий файл
            let configContent = fs.readFileSync(configPath, 'utf8');
            console.log('📖 Читаем текущий trading-config.js...');

            // Обновляем позиции для каждого пула
            for (const [poolName, positionAddress] of Object.entries(newPositions)) {
                const poolKey = poolName; // POOL_1 или POOL_2

                // Ищем строку с позицией и заменяем (более точный regex)
                const regex = new RegExp(`(${poolKey}:\\s*')([^']*)(')`, 'g');
                const replacement = `$1${positionAddress}$3`;

                console.log(`🔍 Ищем паттерн: ${poolKey}: '...'`);

                if (configContent.match(regex)) {
                    const oldContent = configContent;
                    configContent = configContent.replace(regex, replacement);

                    // Проверяем что замена произошла
                    if (oldContent !== configContent) {
                        console.log(`✅ ${poolKey}: обновлен на ${positionAddress.slice(0,8)}...`);
                    } else {
                        console.log(`⚠️ ${poolKey}: замена не произошла`);
                    }
                } else {
                    console.log(`⚠️ ${poolKey}: не найден в конфиге для обновления`);

                    // Показываем что ищем
                    const lines = configContent.split('\n');
                    const matchingLines = lines.filter(line => line.includes(poolKey));
                    console.log(`   Найденные строки с ${poolKey}:`, matchingLines);
                }
            }

            // Записываем обновленный файл
            fs.writeFileSync(configPath, configContent, 'utf8');
            console.log('✅ TRADING-CONFIG.JS УСПЕШНО ОБНОВЛЕН!');

            // Показываем что изменилось
            console.log('\n📋 НОВЫЕ ПОЗИЦИИ В КОНФИГЕ:');
            for (const [poolName, positionAddress] of Object.entries(newPositions)) {
                console.log(`   ${poolName}: ${positionAddress}`);
            }

        } catch (error) {
            console.log(`❌ Ошибка обновления trading-config.js: ${error.message}`);
            throw error;
        }
    }

    /**
     * 🔥 СОЗДАНИЕ INITIALIZE_POSITION ИНСТРУКЦИИ ЧЕРЕЗ ОФИЦИАЛЬНЫЙ SDK
     */
    async createInitializePositionInstructionSDK(positionKeypair, poolAddress, activeBinId) {
        console.log(`🔧 InitializePosition через SDK для пула ${poolAddress.slice(0, 8)}...`);
        console.log(`   Position: ${positionKeypair.publicKey.toString().slice(0, 8)}...`);
        console.log(`   Активный bin ID: ${activeBinId}`);

        try {
            // 🚀 СОЗДАЕМ ANCHOR PROVIDER
            const wallet = new Wallet(this.wallet);
            const provider = new AnchorProvider(this.connection, wallet, {});

            // 🚀 СОЗДАЕМ DLMM POOL ЧЕРЕЗ ОФИЦИАЛЬНЫЙ SDK
            console.log(`   🔧 Создание DLMM pool объекта...`);
            const dlmmPool = await DLMM.create(this.connection, new PublicKey(poolAddress), {
                cluster: 'mainnet-beta'
            });

            console.log(`   ✅ DLMM pool создан`);

            // 🎯 СОЗДАЕМ ИНСТРУКЦИЮ ЧЕРЕЗ SDK С МАКСИМАЛЬНЫМ ДИАПАЗОНОМ БИНОВ (69 BINS - ЛИМИТ METEORA)
            const minBinId = activeBinId - 34; // Активный бин - 34 (максимальный диапазон)
            const maxBinId = activeBinId + 34; // Активный бин + 34 (максимальный диапазон)
            const width = maxBinId - minBinId + 1; // Ширина = 69 бин (МАКСИМУМ!)

            console.log(`   🔥 МАКСИМАЛЬНЫЙ ДИАПАЗОН БИНОВ (69 BINS - ЛИМИТ METEORA):`);
            console.log(`   Min Bin ID: ${minBinId} (активный - 34)`);
            console.log(`   Max Bin ID: ${maxBinId} (активный + 34)`);
            console.log(`   Width: ${width} бинов (максимально допустимый диапазон)`);

            // 🔥 ИСПОЛЬЗУЕМ ОФИЦИАЛЬНЫЙ МЕТОД SDK - initializePositionAndAddLiquidityByStrategy с нулевой ликвидностью
            const BN = require('bn.js');
            const transaction = await dlmmPool.initializePositionAndAddLiquidityByStrategy({
                positionPubKey: positionKeypair.publicKey,
                totalXAmount: new BN(0), // Нулевая ликвидность
                totalYAmount: new BN(0), // Нулевая ликвидность
                strategy: {
                    maxBinId: maxBinId,     // 🔥 ВЕРХНЯЯ ГРАНИЦА ДИАПАЗОНА
                    minBinId: minBinId,     // 🔥 НИЖНЯЯ ГРАНИЦА ДИАПАЗОНА
                    strategyType: 0 // Spot strategy
                },
                user: this.wallet.publicKey,
                slippage: 1 // 1% slippage
            });

            // Извлекаем только initializePosition инструкцию
            const initPositionIx = transaction.instructions.find(ix =>
                ix.programId.toString() === 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo' &&
                ix.data.length >= 8 &&
                ix.data[0] === 219 && ix.data[1] === 192 // discriminator для initializePosition
            );

            console.log(`   ✅ SDK Initialize position инструкция создана`);
            return initPositionIx;

        } catch (error) {
            console.error(`   ❌ Ошибка создания SDK инструкции: ${error.message}`);
            console.error(`   📋 Stack: ${error.stack}`);
            throw error;
        }
    }

    /**
     * 🔥 СОЗДАНИЕ INITIALIZE_POSITION ИНСТРУКЦИИ (РУЧНОЙ МЕТОД - БЭКАП)
     */
    createInitializePositionInstruction(positionKeypair, poolAddress, activeBinId) {
        console.log(`🔧 InitializePosition для пула ${poolAddress.slice(0, 8)}...`);
        console.log(`   Position: ${positionKeypair.publicKey.toString().slice(0, 8)}...`);
        console.log(`   Активный bin ID: ${activeBinId}`);

        // 🔥 ОФИЦИАЛЬНЫЙ METEORA DLMM INITIALIZE POSITION DISCRIMINATOR (ИЗ meteora-discriminators.json)!
        const initializePositionDiscriminator = [219, 192, 234, 71, 190, 191, 102, 80];

        // 🔥 ТОЧНАЯ СТРУКТУРА ДАННЫХ ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ
        const instructionData = Buffer.alloc(16);
        Buffer.from(initializePositionDiscriminator).copy(instructionData, 0);
        
        // Параметры: lower_bin_id (i32) + width (i32) согласно IDL
        const lowerBinId = activeBinId - 34; // 🔥 НИЖНЯЯ ГРАНИЦА ДИАПАЗОНА (МАКСИМАЛЬНЫЙ)
        const width = 69; // 🔥 ШИРИНА = 69 БИН (максимально допустимый диапазон Meteora)

        // Записываем параметры в little-endian формате (i32 = 4 байта каждый)
        instructionData.writeInt32LE(lowerBinId, 8);  // lower_bin_id (i32)
        instructionData.writeInt32LE(width, 12);      // width (i32)
        
        console.log(`   Lower bin ID: ${activeBinId}`);
        console.log(`   Instruction data: ${instructionData.toString('hex')}`);

        const instruction = new TransactionInstruction({
            keys: [
                // #0 - Position (Signer + Writable) - НОВЫЙ KEYPAIR!
                { pubkey: positionKeypair.publicKey, isSigner: true, isWritable: true },
                // #1 - LB Pair (Writable) - АДРЕС ПУЛА
                { pubkey: new PublicKey(poolAddress), isSigner: false, isWritable: true },
                // #2 - User (Signer + Writable) - НАШ WALLET
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: true },
                // #3 - System Program
                { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },
                // #4 - Rent Sysvar
                { pubkey: new PublicKey('SysvarRent111111111111111111111111111111111'), isSigner: false, isWritable: false },
                // #5 - Event Authority
                { pubkey: new PublicKey('D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6'), isSigner: false, isWritable: false },
                // #6 - Program
                { pubkey: this.METEORA_DLMM_PROGRAM, isSigner: false, isWritable: false }
            ],
            programId: this.METEORA_DLMM_PROGRAM,
            data: instructionData
        });

        console.log(`   ✅ Initialize position инструкция создана (7 аккаунтов, 16 байт данных)`);
        return instruction;
    }

    /**
     * 🔥 СОЗДАНИЕ ТРАНЗАКЦИИ ДЛЯ ОБОИХ ПУЛОВ
     */
    async createInitializePositionsTransaction() {
        console.log('\n🔥 СОЗДАНИЕ ТРАНЗАКЦИИ ДЛЯ INITIALIZE POSITIONS...\n');

        // 🔥 ПРОВЕРЯЕМ ЧТО АКТИВНЫЕ BIN ID ПОЛУЧЕНЫ!
        for (const [poolName, poolData] of Object.entries(this.POOLS)) {
            if (poolData.activeBinId === null) {
                throw new Error(`❌ Активный bin ID не получен для ${poolName}! Вызовите getCurrentActiveBinIds() сначала.`);
            }
        }

        const instructions = [];
        const signers = [this.wallet];
        const positionKeypairs = [];

        // Добавляем ComputeBudget инструкции
        instructions.push(
            ComputeBudgetProgram.setComputeUnitLimit({ units: 400000 }),
            ComputeBudgetProgram.setComputeUnitPrice({ microLamports: 1000 })
        );

        // Создаем позиции для обоих пулов
        for (const [poolName, poolData] of Object.entries(this.POOLS)) {
            console.log(`📊 ${poolName}: ${poolData.address} (активный bin: ${poolData.activeBinId})`);
            
            // Создаем новый keypair для позиции
            const positionKeypair = new Keypair();
            positionKeypairs.push({
                pool: poolName,
                address: poolData.address,
                positionAddress: positionKeypair.publicKey.toString(),
                secretKey: Array.from(positionKeypair.secretKey), // 🔥 СОХРАНЯЕМ PRIVATE KEY!
                activeBinId: poolData.activeBinId
            });
            
            // Добавляем signer
            signers.push(positionKeypair);
            
            // 🚀 ПРОБУЕМ SDK МЕТОД
            try {
                const initInstruction = await this.createInitializePositionInstructionSDK(
                    positionKeypair,
                    poolData.address,
                    poolData.activeBinId
                );

                instructions.push(initInstruction);
                console.log(`   ✅ ${poolName} initialize_position добавлена (SDK)\n`);

            } catch (sdkError) {
                console.log(`   ⚠️ SDK ошибка: ${sdkError.message}`);
                console.log(`   🔄 Пробуем ручной метод...`);

                // 🔄 FALLBACK НА РУЧНОЙ МЕТОД
                const initInstruction = this.createInitializePositionInstruction(
                    positionKeypair,
                    poolData.address,
                    poolData.activeBinId
                );

                instructions.push(initInstruction);
                console.log(`   ✅ ${poolName} initialize_position добавлена (ручной)\n`);
            }
        }

        console.log(`📊 ИТОГО:`);
        console.log(`   Инструкций: ${instructions.length}`);
        console.log(`   Signers: ${signers.length}`);
        console.log(`   Position keypairs: ${positionKeypairs.length}`);

        // Создаем транзакцию
        const transaction = new Transaction();
        transaction.add(...instructions);
        
        // Получаем recent blockhash
        const { blockhash } = await this.connection.getLatestBlockhash();
        transaction.recentBlockhash = blockhash;
        transaction.feePayer = this.wallet.publicKey;

        // Подписываем транзакцию
        transaction.sign(...signers);

        console.log(`\n📊 ТРАНЗАКЦИЯ ГОТОВА:`);
        console.log(`   Размер: ${transaction.serialize().length} байт`);
        console.log(`   Blockhash: ${blockhash.slice(0, 8)}...`);

        return {
            transaction,
            positionKeypairs,
            signers
        };
    }

    /**
     * 🚀 ОТПРАВКА ТРАНЗАКЦИИ
     */
    async sendInitializePositionsTransaction() {
        try {
            console.log('🚀 СОЗДАНИЕ И ОТПРАВКА INITIALIZE POSITIONS ТРАНЗАКЦИИ...\n');
            
            // Создаем транзакцию
            const { transaction, positionKeypairs } = await this.createInitializePositionsTransaction();
            
            // ПРОПУСКАЕМ СИМУЛЯЦИЮ - ОТПРАВЛЯЕМ НАПРЯМУЮ!
            console.log('⚡ ПРОПУСКАЕМ СИМУЛЯЦИЮ - ОТПРАВЛЯЕМ НАПРЯМУЮ!');
            
            // Отправляем транзакцию
            console.log('\n📤 ОТПРАВКА ТРАНЗАКЦИИ...');
            const signature = await this.connection.sendRawTransaction(transaction.serialize(), {
                skipPreflight: false,
                preflightCommitment: 'confirmed'
            });
            
            console.log(`✅ ТРАНЗАКЦИЯ ОТПРАВЛЕНА!`);
            console.log(`   Signature: ${signature}`);
            
            // Ждем подтверждения
            console.log('⏳ ОЖИДАНИЕ ПОДТВЕРЖДЕНИЯ...');
            const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');
            
            if (confirmation.value.err) {
                console.error('❌ ОШИБКА ПОДТВЕРЖДЕНИЯ:', confirmation.value.err);
                return null;
            }
            
            console.log('🎉 ТРАНЗАКЦИЯ ПОДТВЕРЖДЕНА!');
            
            // Сохраняем адреса позиций
            const positionData = {
                timestamp: new Date().toISOString(),
                signature: signature,
                positions: positionKeypairs
            };
            
            fs.writeFileSync('position-addresses.json', JSON.stringify(positionData, null, 2));
            console.log('\n💾 Адреса позиций сохранены в position-addresses.json');
            
            // Выводим результат
            console.log('\n📋 СОЗДАННЫЕ ПОЗИЦИИ:');
            positionKeypairs.forEach((pos, i) => {
                console.log(`   ${pos.pool}: ${pos.positionAddress}`);
                console.log(`     Pool: ${pos.address}`);
                console.log(`     Active Bin ID: ${pos.activeBinId}`);
            });
            
            return {
                signature,
                positionKeypairs,
                success: true
            };
            
        } catch (error) {
            console.error('❌ ОШИБКА СОЗДАНИЯ ПОЗИЦИЙ:', error.message);
            return null;
        }
    }
}

// 🚀 ЗАПУСК
async function runInitializePositions() {
    try {
        console.log('🔥 INITIALIZE POSITIONS ONLY - ЗАПУСК\n');

        const initializer = new InitializePositionsOnly();

        // 🔥 СНАЧАЛА ПОЛУЧАЕМ ТЕКУЩИЕ АКТИВНЫЕ BIN ID!
        console.log('📊 ШАГ 1: ПОЛУЧЕНИЕ ТЕКУЩИХ АКТИВНЫХ BIN ID...');
        await initializer.getCurrentActiveBinIds();

        console.log('\n🚀 ШАГ 2: СОЗДАНИЕ И ОТПРАВКА ТРАНЗАКЦИИ...');
        const result = await initializer.sendInitializePositionsTransaction();

        if (result && result.success) {
            console.log('\n🎉 ВСЕ ПОЗИЦИИ УСПЕШНО СОЗДАНЫ!');
            console.log(`   Signature: ${result.signature}`);
            console.log(`   Позиций создано: ${result.positionKeypairs.length}`);

            // 🔥 ШАГ 3: ОБНОВЛЯЕМ TRADING-CONFIG.JS С НОВЫМИ ПОЗИЦИЯМИ!
            console.log('\n🔧 ШАГ 3: ОБНОВЛЕНИЕ TRADING-CONFIG.JS...');

            // Формируем объект с новыми позициями
            const newPositions = {};
            result.positionKeypairs.forEach(pos => {
                newPositions[pos.pool] = pos.positionAddress;
            });

            // Обновляем конфиг
            await initializer.updateTradingConfig(newPositions);

            console.log('\n🎉 ПОЛНЫЙ УСПЕХ! ПОЗИЦИИ СОЗДАНЫ И КОНФИГ ОБНОВЛЕН!');

        } else {
            console.log('\n❌ ОШИБКА СОЗДАНИЯ ПОЗИЦИЙ');
        }
        
    } catch (error) {
        console.error('❌ КРИТИЧЕСКАЯ ОШИБКА:', error.message);
    }
}

// Запускаем если файл вызван напрямую
if (require.main === module) {
    runInitializePositions();
}

// Экспорт для тестирования
module.exports = InitializePositionsOnly;
