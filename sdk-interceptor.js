/**
 * 🔍 SDK ПЕРЕХВАТЧИК - ИЗУЧАЕМ КАК METEORA SDK СОЗДАЕТ add_liquidity2
 * 
 * ЦЕЛЬ: Перехватить процесс сериализации remaining_accounts_info
 * и скопировать точную логику в наш ручной код
 */

const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
require('dotenv').config({ path: '.env.solana' });

// Исследуем что экспортирует SDK
console.log('🔍 ИССЛЕДУЕМ METEORA SDK...');
const dlmmModule = require('@meteora-ag/dlmm');
console.log('🔍 SDK MODULE:', Object.keys(dlmmModule));
console.log('🔍 SDK MODULE TYPE:', typeof dlmmModule);

// Пробуем разные варианты импорта SDK
let DLMM;
if (dlmmModule.default) {
    DLMM = dlmmModule.default;
    console.log('✅ DLMM найден как .default');
    console.log('🔍 DEFAULT методы:', Object.getOwnPropertyNames(DLMM));
    if (DLMM.create) {
        console.log('✅ DLMM.create найден!');
    }
} else if (dlmmModule.DLMM) {
    DLMM = dlmmModule.DLMM;
    console.log('✅ DLMM найден как .DLMM');
} else {
    console.log('❌ DLMM НЕ НАЙДЕН, пробуем другие варианты...');

    // Ищем функции с create методом
    for (const [key, value] of Object.entries(dlmmModule)) {
        if (value && typeof value === 'object' && value.create) {
            DLMM = value;
            console.log(`✅ Найден ${key} с методом create`);
            break;
        }
    }
}

// 🔧 КОНФИГУРАЦИЯ ДЛЯ ПЕРЕХВАТА
const CONFIG = {
    // Наши данные из рабочего кода
    POOL_ADDRESS: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
    POSITION_ADDRESS: 'DLyJBeMqT9kextYk7yPY6WHtfwBJHYci1AvVgyGWi6GA',
    
    // Суммы из нашего анализатора
    AMOUNT_X: 17652, // WSOL
    AMOUNT_Y: 1000,  // USDC
    
    // RPC
    RPC_URL: 'https://api.mainnet-beta.solana.com',
    
    // Кошелек (только для создания инструкции, не отправляем!)
    WALLET_PRIVATE_KEY: process.env.PRIVATE_KEY
};

class SDKInterceptor {
    constructor() {
        console.log('🔍 ИНИЦИАЛИЗАЦИЯ SDK ПЕРЕХВАТЧИКА...');

        // Диагностика SDK
        console.log('🔍 ДИАГНОСТИКА SDK:');
        console.log(`   DLMM тип: ${typeof DLMM}`);
        console.log(`   DLMM: ${DLMM}`);
        if (DLMM) {
            console.log(`   DLMM.create: ${typeof DLMM.create}`);
            console.log(`   DLMM методы: ${Object.getOwnPropertyNames(DLMM)}`);
        }

        // Подключение
        this.connection = new Connection(CONFIG.RPC_URL, 'confirmed');

        // Кошелек
        let privateKeyArray;
        try {
            privateKeyArray = JSON.parse(CONFIG.WALLET_PRIVATE_KEY);
        } catch (error) {
            console.log('⚠️ Ошибка парсинга приватного ключа, используем тестовый кошелек');
            privateKeyArray = Array.from(Keypair.generate().secretKey);
        }
        this.wallet = Keypair.fromSecretKey(new Uint8Array(privateKeyArray));

        console.log(`✅ Кошелек: ${this.wallet.publicKey.toString()}`);
        console.log(`✅ RPC: ${CONFIG.RPC_URL}`);
    }

    /**
     * 🔍 ОСНОВНАЯ ФУНКЦИЯ ПЕРЕХВАТА
     */
    async interceptAddLiquidity2() {
        console.log('\n🔍🔍🔍 НАЧИНАЕМ ПЕРЕХВАТ SDK add_liquidity2 🔍🔍🔍');
        
        try {
            // 1. Загружаем пул через SDK
            console.log('\n📊 ШАГ 1: ЗАГРУЗКА ПУЛА ЧЕРЕЗ SDK...');
            const poolPubkey = new PublicKey(CONFIG.POOL_ADDRESS);
            const dlmm = await DLMM.create(this.connection, poolPubkey);
            
            console.log(`✅ Пул загружен: ${poolPubkey.toString()}`);
            console.log(`📊 Активный бин ID: ${dlmm.lbPair.activeId}`);
            
            // 2. Перехватываем создание инструкции
            console.log('\n🔍 ШАГ 2: ПЕРЕХВАТ СОЗДАНИЯ add_liquidity2 ИНСТРУКЦИИ...');
            
            // Подготавливаем параметры как в нашем коде
            const positionPubkey = new PublicKey(CONFIG.POSITION_ADDRESS);
            const amountX = CONFIG.AMOUNT_X;
            const amountY = CONFIG.AMOUNT_Y;
            const activeBinId = dlmm.lbPair.activeId;
            
            console.log(`🎯 ПАРАМЕТРЫ ДЛЯ SDK:`);
            console.log(`   Position: ${positionPubkey.toString()}`);
            console.log(`   Amount X (WSOL): ${amountX}`);
            console.log(`   Amount Y (USDC): ${amountY}`);
            console.log(`   Active Bin ID: ${activeBinId}`);
            
            // 3. ПЕРЕХВАТЫВАЕМ ПРОЦЕСС СОЗДАНИЯ ИНСТРУКЦИИ
            await this.interceptInstructionCreation(dlmm, positionPubkey, amountX, amountY, activeBinId);
            
        } catch (error) {
            console.error('❌ ОШИБКА В ПЕРЕХВАТЧИКЕ:', error);
            console.error('Stack:', error.stack);
        }
    }

    /**
     * 🔍 ПЕРЕХВАТ СОЗДАНИЯ ИНСТРУКЦИИ
     */
    async interceptInstructionCreation(dlmm, positionPubkey, amountX, amountY, activeBinId) {
        console.log('\n🔍 ПЕРЕХВАТЫВАЕМ СОЗДАНИЕ ИНСТРУКЦИИ...');
        
        try {
            // Создаем bin liquidity distribution как в нашем коде
            const binLiquidityDist = [{
                binId: activeBinId,
                distributionX: 10000, // 100%
                distributionY: 10000  // 100%
            }];
            
            console.log(`🎯 BIN LIQUIDITY DISTRIBUTION:`);
            console.log(`   Bin ID: ${activeBinId}`);
            console.log(`   Distribution X: 10000`);
            console.log(`   Distribution Y: 10000`);
            
            // ПЕРЕХВАТЫВАЕМ МЕТОД SDK
            console.log('\n🔍 ВЫЗЫВАЕМ SDK addLiquidity2...');
            
            // Патчим Buffer.from для перехвата сериализации
            this.patchBufferMethods();
            
            // Патчим PublicKey.toBuffer для перехвата
            this.patchPublicKeyMethods();
            
            // Вызываем SDK метод
            const addLiquidityTx = await dlmm.addLiquidity2({
                position: positionPubkey,
                user: this.wallet.publicKey,
                totalXAmount: amountX,
                totalYAmount: amountY,
                binLiquidityDist: binLiquidityDist
            });
            
            console.log('\n✅ SDK СОЗДАЛ ИНСТРУКЦИЮ!');
            console.log(`📊 Количество инструкций: ${addLiquidityTx.instructions.length}`);
            
            // Анализируем созданную инструкцию
            await this.analyzeSDKInstruction(addLiquidityTx);
            
        } catch (error) {
            console.error('❌ ОШИБКА В СОЗДАНИИ ИНСТРУКЦИИ:', error);
            console.error('Stack:', error.stack);
        }
    }

    /**
     * 🔍 ПАТЧИМ BUFFER МЕТОДЫ ДЛЯ ПЕРЕХВАТА
     */
    patchBufferMethods() {
        console.log('\n🔍 ПАТЧИМ Buffer.from ДЛЯ ПЕРЕХВАТА СЕРИАЛИЗАЦИИ...');
        
        const originalBufferFrom = Buffer.from;
        const originalBufferAlloc = Buffer.alloc;
        
        // Перехватываем Buffer.from
        Buffer.from = function(...args) {
            const result = originalBufferFrom.apply(this, args);
            
            // Логируем только интересные буферы
            if (args[0] && typeof args[0] === 'object' && args[0].length > 10) {
                console.log(`🔍 BUFFER.FROM ПЕРЕХВАЧЕН:`);
                console.log(`   Размер: ${result.length} байт`);
                console.log(`   HEX: ${result.toString('hex').substring(0, 100)}...`);
                console.log(`   Первые 10 байтов: [${Array.from(result.slice(0, 10)).join(', ')}]`);
            }
            
            return result;
        };
        
        // Перехватываем Buffer.alloc
        Buffer.alloc = function(size, fill, encoding) {
            const result = originalBufferAlloc.call(this, size, fill, encoding);
            
            // Логируем большие буферы
            if (size > 50) {
                console.log(`🔍 BUFFER.ALLOC ПЕРЕХВАЧЕН: ${size} байт`);
            }
            
            return result;
        };
    }

    /**
     * 🔍 ПАТЧИМ PUBLICKEY МЕТОДЫ
     */
    patchPublicKeyMethods() {
        console.log('\n🔍 ПАТЧИМ PublicKey.toBuffer ДЛЯ ПЕРЕХВАТА...');
        
        const originalToBuffer = PublicKey.prototype.toBuffer;
        const originalToBytes = PublicKey.prototype.toBytes;
        
        PublicKey.prototype.toBuffer = function() {
            const result = originalToBuffer.call(this);
            console.log(`🔍 PUBLICKEY.TOBUFFER: ${this.toString()} → ${result.toString('hex').substring(0, 16)}...`);
            return result;
        };
        
        PublicKey.prototype.toBytes = function() {
            const result = originalToBytes.call(this);
            console.log(`🔍 PUBLICKEY.TOBYTES: ${this.toString()} → ${Buffer.from(result).toString('hex').substring(0, 16)}...`);
            return result;
        };
    }

    /**
     * 🔍 АНАЛИЗ СОЗДАННОЙ SDK ИНСТРУКЦИИ
     */
    async analyzeSDKInstruction(addLiquidityTx) {
        console.log('\n🔍🔍🔍 АНАЛИЗ SDK ИНСТРУКЦИИ 🔍🔍🔍');
        
        // Ищем add_liquidity2 инструкцию
        const addLiquidityInstruction = addLiquidityTx.instructions.find(ix => 
            ix.data && ix.data.length > 100 // add_liquidity2 имеет ~106 байт
        );
        
        if (!addLiquidityInstruction) {
            console.log('❌ НЕ НАЙДЕНА add_liquidity2 ИНСТРУКЦИЯ');
            return;
        }
        
        console.log(`✅ НАЙДЕНА add_liquidity2 ИНСТРУКЦИЯ:`);
        console.log(`📊 Размер data: ${addLiquidityInstruction.data.length} байт`);
        console.log(`📊 Количество аккаунтов: ${addLiquidityInstruction.keys.length}`);
        
        // ДЕТАЛЬНЫЙ АНАЛИЗ INSTRUCTION DATA
        this.analyzeInstructionData(addLiquidityInstruction.data);
        
        // АНАЛИЗ АККАУНТОВ
        this.analyzeInstructionAccounts(addLiquidityInstruction.keys);
    }

    /**
     * 🔍 ДЕТАЛЬНЫЙ АНАЛИЗ INSTRUCTION DATA
     */
    analyzeInstructionData(data) {
        console.log('\n🔍 ДЕТАЛЬНЫЙ АНАЛИЗ INSTRUCTION DATA:');
        console.log(`📊 Общий размер: ${data.length} байт`);
        console.log(`🔍 ПОЛНЫЙ HEX: ${data.toString('hex')}`);
        
        // Разбираем по частям
        let offset = 0;
        
        // Discriminator (8 байт)
        const discriminator = data.slice(offset, offset + 8);
        console.log(`🔍 DISCRIMINATOR (0-7): ${discriminator.toString('hex')}`);
        offset += 8;
        
        // Amount X (8 байт)
        const amountX = data.readBigUInt64LE(offset);
        console.log(`🔍 AMOUNT_X (8-15): ${amountX} (${data.slice(offset, offset + 8).toString('hex')})`);
        offset += 8;
        
        // Amount Y (8 байт)
        const amountY = data.readBigUInt64LE(offset);
        console.log(`🔍 AMOUNT_Y (16-23): ${amountY} (${data.slice(offset, offset + 8).toString('hex')})`);
        offset += 8;
        
        // Bin Liquidity Dist Vec Length (4 байта)
        const binDistLength = data.readUInt32LE(offset);
        console.log(`🔍 BIN_DIST_LENGTH (24-27): ${binDistLength} (${data.slice(offset, offset + 4).toString('hex')})`);
        offset += 4;
        
        // Bin ID (4 байта)
        const binId = data.readInt32LE(offset);
        console.log(`🔍 BIN_ID (28-31): ${binId} (${data.slice(offset, offset + 4).toString('hex')})`);
        offset += 4;
        
        // Distribution X (2 байта)
        const distX = data.readUInt16LE(offset);
        console.log(`🔍 DISTRIBUTION_X (32-33): ${distX} (${data.slice(offset, offset + 2).toString('hex')})`);
        offset += 2;
        
        // Distribution Y (2 байта)
        const distY = data.readUInt16LE(offset);
        console.log(`🔍 DISTRIBUTION_Y (34-35): ${distY} (${data.slice(offset, offset + 2).toString('hex')})`);
        offset += 2;
        
        // REMAINING_ACCOUNTS_INFO - САМОЕ ВАЖНОЕ!
        console.log(`\n🔥🔥🔥 REMAINING_ACCOUNTS_INFO АНАЛИЗ (offset ${offset}): 🔥🔥🔥`);
        
        // Vec length (4 байта)
        const remainingVecLength = data.readUInt32LE(offset);
        console.log(`🔍 REMAINING_VEC_LENGTH (${offset}-${offset+3}): ${remainingVecLength} (${data.slice(offset, offset + 4).toString('hex')})`);
        offset += 4;
        
        // Анализируем структуру remaining_accounts_info
        this.analyzeRemainingAccountsInfo(data, offset);
    }

    /**
     * 🔥 АНАЛИЗ REMAINING_ACCOUNTS_INFO - КЛЮЧЕВАЯ ФУНКЦИЯ!
     */
    analyzeRemainingAccountsInfo(data, startOffset) {
        console.log(`\n🔥🔥🔥 ДЕТАЛЬНЫЙ АНАЛИЗ REMAINING_ACCOUNTS_INFO 🔥🔥🔥`);
        
        let offset = startOffset;
        
        try {
            // Читаем все оставшиеся байты
            const remainingData = data.slice(offset);
            console.log(`🔍 ОСТАВШИЕСЯ ДАННЫЕ: ${remainingData.length} байт`);
            console.log(`🔍 HEX: ${remainingData.toString('hex')}`);
            
            // Пытаемся разобрать как наша структура
            if (remainingData.length >= 9) {
                // Slices count (4 байта)
                const slicesCount = remainingData.readUInt32LE(0);
                console.log(`🔍 SLICES_COUNT (0-3): ${slicesCount} (${remainingData.slice(0, 4).toString('hex')})`);
                
                // Accounts length (4 байта)
                const accountsLength = remainingData.readUInt32LE(4);
                console.log(`🔍 ACCOUNTS_LENGTH (4-7): ${accountsLength} (${remainingData.slice(4, 8).toString('hex')})`);
                
                // Accounts type (1 байт) - ЭТО КЛЮЧЕВОЕ!
                const accountsType = remainingData.readUInt8(8);
                console.log(`🔥🔥🔥 ACCOUNTS_TYPE (8): ${accountsType} (${remainingData.slice(8, 9).toString('hex')}) 🔥🔥🔥`);
                
                // Определяем тип
                const typeNames = {
                    0: 'BinArray',
                    1: 'TransferHookX', 
                    2: 'TransferHookY',
                    3: 'TransferHookReward'
                };
                
                console.log(`🎯 РАСШИФРОВКА: accountsType = ${accountsType} = ${typeNames[accountsType] || 'UNKNOWN'}`);
                
                // Pubkeys
                let pubkeyOffset = 9;
                for (let i = 0; i < accountsLength && pubkeyOffset + 32 <= remainingData.length; i++) {
                    const pubkeyBytes = remainingData.slice(pubkeyOffset, pubkeyOffset + 32);
                    const pubkey = new PublicKey(pubkeyBytes);
                    console.log(`🔍 PUBKEY[${i}] (${pubkeyOffset}-${pubkeyOffset+31}): ${pubkey.toString()}`);
                    pubkeyOffset += 32;
                }
            }
            
        } catch (error) {
            console.error('❌ ОШИБКА В АНАЛИЗЕ REMAINING_ACCOUNTS_INFO:', error);
        }
    }

    /**
     * 🔍 АНАЛИЗ АККАУНТОВ ИНСТРУКЦИИ
     */
    analyzeInstructionAccounts(keys) {
        console.log(`\n🔍 АНАЛИЗ АККАУНТОВ ИНСТРУКЦИИ (${keys.length} аккаунтов):`);
        
        keys.forEach((key, index) => {
            console.log(`   [${index.toString().padStart(2, '0')}] ${key.pubkey.toString()} (signer: ${key.isSigner}, writable: ${key.isWritable})`);
        });
    }
}

// 🚀 ЗАПУСК ПЕРЕХВАТЧИКА
async function main() {
    console.log('🔍🔍🔍 METEORA SDK ПЕРЕХВАТЧИК ЗАПУЩЕН 🔍🔍🔍');
    console.log('ЦЕЛЬ: Изучить как SDK создает remaining_accounts_info');
    
    const interceptor = new SDKInterceptor();
    await interceptor.interceptAddLiquidity2();
    
    console.log('\n🎯 ПЕРЕХВАТ ЗАВЕРШЕН!');
}

// Запуск только если файл вызван напрямую
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { SDKInterceptor };
