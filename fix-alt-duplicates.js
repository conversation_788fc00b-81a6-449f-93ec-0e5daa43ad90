// 🔥 ИСПРАВЛЕНИЕ ДУБЛИРОВАНИЯ В ALT ТАБЛИЦАХ
const fs = require('fs');

console.log('🔥 ИСПРАВЛЕНИЕ ДУБЛИРОВАНИЯ В ALT ТАБЛИЦАХ...');

// Загружаем текущие ALT данные
const altData = JSON.parse(fs.readFileSync('./custom-alt-data.json', 'utf8'));

console.log(`📊 ЗАГРУЖЕНО: ${altData.totalTables} таблиц, ${altData.totalAccounts} адресов`);

// Собираем все адреса и находим дублирования
const allAddresses = [];
const addressToTable = {};

Object.entries(altData.tables).forEach(([tableName, tableData]) => {
    tableData.addresses.forEach(address => {
        allAddresses.push(address);
        
        if (!addressToTable[address]) {
            addressToTable[address] = [];
        }
        addressToTable[address].push(tableName);
    });
});

// Находим дублированные адреса
const duplicatedAddresses = [];
Object.entries(addressToTable).forEach(([address, tables]) => {
    if (tables.length > 1) {
        duplicatedAddresses.push({ address, tables });
    }
});

console.log(`🚨 НАЙДЕНО ${duplicatedAddresses.length} ДУБЛИРОВАННЫХ АДРЕСОВ:`);
duplicatedAddresses.forEach((dup, i) => {
    console.log(`   ${i + 1}. ${dup.address} (в таблицах: ${dup.tables.join(', ')})`);
});

// СТРАТЕГИЯ ИСПРАВЛЕНИЯ:
// 1. Оставляем дублированные адреса ТОЛЬКО в marginfi1 таблице
// 2. Удаляем их из custom таблицы
console.log(`🔧 ИСПРАВЛЯЕМ ДУБЛИРОВАНИЕ...`);

const fixedTables = {};

// Копируем marginfi1 таблицу без изменений
fixedTables.marginfi1 = {
    ...altData.tables.marginfi1
};

// Исправляем custom таблицу - убираем дублированные адреса
const customAddresses = altData.tables.custom.addresses;
const fixedCustomAddresses = customAddresses.filter(address => {
    const isDuplicated = duplicatedAddresses.some(dup => dup.address === address);
    return !isDuplicated; // Оставляем только НЕ дублированные
});

fixedTables.custom = {
    ...altData.tables.custom,
    addresses: fixedCustomAddresses,
    accountCount: fixedCustomAddresses.length
};

// Подсчитываем новую статистику
const totalFixedAccounts = fixedTables.marginfi1.accountCount + fixedTables.custom.accountCount;

const fixedAltData = {
    timestamp: new Date().toISOString(),
    source: "🔥 ИСПРАВЛЕНО ДУБЛИРОВАНИЕ МЕЖДУ ALT ТАБЛИЦАМИ",
    totalTables: 2,
    totalAccounts: totalFixedAccounts,
    tables: fixedTables
};

console.log(`✅ ИСПРАВЛЕНИЕ ЗАВЕРШЕНО:`);
console.log(`   marginfi1: ${fixedTables.marginfi1.accountCount} адресов`);
console.log(`   custom: ${fixedTables.custom.accountCount} адресов (было ${customAddresses.length})`);
console.log(`   Удалено дублированных: ${duplicatedAddresses.length}`);
console.log(`   Общий размер: ${totalFixedAccounts} адресов (было ${altData.totalAccounts})`);

// Сохраняем исправленные данные
fs.writeFileSync('./custom-alt-data.json', JSON.stringify(fixedAltData, null, 2));

console.log(`💾 ИСПРАВЛЕННЫЕ ALT ТАБЛИЦЫ СОХРАНЕНЫ В custom-alt-data.json`);

// Проверяем что дублирования больше нет
const allFixedAddresses = [];
Object.values(fixedTables).forEach(table => {
    allFixedAddresses.push(...table.addresses);
});

const uniqueFixedAddresses = [...new Set(allFixedAddresses)];

if (allFixedAddresses.length === uniqueFixedAddresses.length) {
    console.log(`✅ ПРОВЕРКА ПРОЙДЕНА: НЕТ ДУБЛИРОВАНИЯ!`);
    console.log(`   Всего адресов: ${allFixedAddresses.length}`);
    console.log(`   Уникальных: ${uniqueFixedAddresses.length}`);
} else {
    console.log(`❌ ОШИБКА: ВСЕ ЕЩЕ ЕСТЬ ДУБЛИРОВАНИЕ!`);
    console.log(`   Всего адресов: ${allFixedAddresses.length}`);
    console.log(`   Уникальных: ${uniqueFixedAddresses.length}`);
}

console.log(`🎉 ИСПРАВЛЕНИЕ ALT ТАБЛИЦ ЗАВЕРШЕНО!`);
